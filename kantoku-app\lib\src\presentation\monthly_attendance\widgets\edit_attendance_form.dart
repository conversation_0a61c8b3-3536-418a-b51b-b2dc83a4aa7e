// ignore_for_file: avoid_positional_boolean_parameters

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../core/utils/custom_time_picker.dart';
import '../../../core/utils/intl_helper.dart';
import '../../../domain/entities/entities.dart';
import 'working_location_selection.dart';

enum WorkingLocationSelectionType {
  none,
  sameAsProject,
  otherProject,
  remote,
  inputText,
}

class EditAttendance extends StatefulWidget {
  const EditAttendance({
    super.key,
    required this.logHours,
    required this.projectList,
    required this.onSave,
    required this.onDelete,
    required this.onBack,
    required this.locale,
  });

  final Locale locale;
  final LogHoursDocs logHours;
  final List<LogHoursProjectLoadItems?> projectList;
  final Function(LogHoursDocs) onSave;
  final Function(LogHoursDocs) onDelete;
  final Function() onBack;

  @override
  State<EditAttendance> createState() => _EditAttendanceState();
}

class _EditAttendanceState extends State<EditAttendance> {
  late LogHoursDocs _editedLogHours;
  late final LogHoursDocs _originalLogHours;
  final FocusNode _workingLocationFocus = FocusNode();
  final FocusNode _descriptionFocus = FocusNode();

  WorkingLocationSelectionType _selectionType =
      WorkingLocationSelectionType.none;
  String? _selectedWorkingLocationDisplay;
  late TextEditingController _workingLocationController;

  @override
  void initState() {
    super.initState();
    _originalLogHours = widget.logHours;
    _editedLogHours = widget.logHours.copyWith();
    _workingLocationController = TextEditingController();

    final initialWorkingLocation = _editedLogHours.workingLocation;
    if (initialWorkingLocation == 'REMOTE') {
      _selectionType = WorkingLocationSelectionType.remote;
    } else if (initialWorkingLocation != null &&
        initialWorkingLocation.isNotEmpty) {
      // Try to match with current project
      LogHoursProjectLoadItems? currentProject;
      if (_editedLogHours.projectId != null) {
        final matchingProjects = widget.projectList
            .where(
              (final p) => p?.id == _editedLogHours.projectId,
            )
            .toList();
        if (matchingProjects.isNotEmpty) {
          currentProject = matchingProjects.first;
        }
      }

      if (currentProject != null &&
          currentProject.address == initialWorkingLocation) {
        _selectionType = WorkingLocationSelectionType.sameAsProject;
        _selectedWorkingLocationDisplay = initialWorkingLocation;
      } else {
        _selectionType = WorkingLocationSelectionType.inputText;
        _selectedWorkingLocationDisplay = initialWorkingLocation;
        _workingLocationController.text = initialWorkingLocation;
      }
    } else {
      _selectionType = WorkingLocationSelectionType.none;
      _selectedWorkingLocationDisplay = null;
    }
  }

  @override
  void dispose() {
    _workingLocationFocus.dispose();
    _descriptionFocus.dispose();
    _workingLocationController.dispose(); // Added
    super.dispose();
  }

  void _unfocus() {
    _workingLocationFocus.unfocus();
    _descriptionFocus.unfocus();
  }

  void _resetToOriginal() {
    setState(() {
      _editedLogHours =
          _originalLogHours.copyWith(); // Use copyWith for mutable copy
      // Reset working location based on _originalLogHours
      final originalWorkingLocation = _originalLogHours.workingLocation;
      if (originalWorkingLocation == 'REMOTE') {
        _selectionType = WorkingLocationSelectionType.remote;
        _workingLocationController.clear();
      } else if (originalWorkingLocation != null &&
          originalWorkingLocation.isNotEmpty) {
        LogHoursProjectLoadItems? currentProject;
        if (_originalLogHours.projectId != null) {
          final matchingProjects = widget.projectList
              .where(
                (final p) => p?.id == _originalLogHours.projectId,
              )
              .toList();
          if (matchingProjects.isNotEmpty) {
            currentProject = matchingProjects.first;
          }
        }

        if (currentProject != null &&
            currentProject.address == originalWorkingLocation) {
          _selectionType = WorkingLocationSelectionType.sameAsProject;
          _selectedWorkingLocationDisplay = originalWorkingLocation;
          _workingLocationController.clear();
        } else {
          _selectionType = WorkingLocationSelectionType.inputText;
          _selectedWorkingLocationDisplay = originalWorkingLocation;
          _workingLocationController.text = originalWorkingLocation;
        }
      } else {
        _selectionType = WorkingLocationSelectionType.none;
        _selectedWorkingLocationDisplay = null;
        _workingLocationController.clear();
      }
    });
  }

  // Copied and adapted from CreateAttendanceForm
  Future<void> _showWorkingLocationSelectionDialog() async {
    if (!context.mounted) {
      return;
    }
    final selectedOption =
        await showModalBottomSheet<WorkingLocationSelectionType>(
      context: context,
      builder: (final BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.location_city),
                title:
                    Text(context.l10n.currentProject), // Ensure l10n keys exist
                onTap: () => Navigator.of(context)
                    .pop(WorkingLocationSelectionType.sameAsProject),
              ),
              ListTile(
                leading: const Icon(Icons.business),
                title:
                    Text(context.l10n.otherProject), // Ensure l10n keys exist
                trailing: const Icon(Icons.chevron_right),
                onTap: () => Navigator.of(context)
                    .pop(WorkingLocationSelectionType.otherProject),
              ),
              ListTile(
                leading: const Icon(Icons.public),
                title: Text(context.l10n.remote), // Ensure l10n keys exist
                onTap: () => Navigator.of(context)
                    .pop(WorkingLocationSelectionType.remote),
              ),
              ListTile(
                leading: const Icon(Icons.edit_location_alt),
                title: Text(
                  context.l10n.enterWorkplace, // Ensure l10n keys exist
                ),
                onTap: () => Navigator.of(context)
                    .pop(WorkingLocationSelectionType.inputText),
              ),
            ],
          ),
        );
      },
    );

    if (selectedOption != null) {
      String? displayValue;
      String? actualValue;
      WorkingLocationSelectionType newSelectionType = selectedOption;

      switch (selectedOption) {
        case WorkingLocationSelectionType.sameAsProject:
          final selectedProjectId = _editedLogHours.projectId;
          if (selectedProjectId != null && selectedProjectId.isNotEmpty) {
            final project = widget.projectList.firstWhere(
              (final p) => p?.id == selectedProjectId,
            );
            if (project?.address != null && project!.address!.isNotEmpty) {
              displayValue = project.address;
              actualValue = project.address;
            } else {
              if (!mounted) {
                return;
              }
              displayValue = context.l10n.noData;
              actualValue = null;
              newSelectionType = WorkingLocationSelectionType.none;
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.l10n.noData),
                ),
              );
            }
          } else {
            if (!mounted) {
              return;
            }
            displayValue = context.l10n.selectProjectFirst;
            actualValue = null;
            newSelectionType = WorkingLocationSelectionType.none;
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(context.l10n.selectProjectFirst),
              ),
            );
          }
          break;
        case WorkingLocationSelectionType.otherProject:
          if (!mounted) {
            return;
          }
          // Filter out nulls from projectList before passing
          final List<LogHoursProjectLoadItems> nonNullProjectList =
              widget.projectList.whereType<LogHoursProjectLoadItems>().toList();
          final address = await Navigator.of(context).push<String>(
            MaterialPageRoute(
              builder: (final context) => WorkingLocationSelection(
                projectList: nonNullProjectList, // Pass non-null list
              ),
            ),
          );
          if (address != null && address.isNotEmpty) {
            displayValue = address;
            actualValue = address;
          } else {
            newSelectionType =
                WorkingLocationSelectionType.none; // Revert if no selection
          }
          break;
        case WorkingLocationSelectionType.remote:
          if (!mounted) {
            return;
          }
          displayValue =
              widget.locale == const Locale('ja') ? 'リモート' : 'Remote';
          actualValue = 'REMOTE';
          break;
        case WorkingLocationSelectionType.inputText:
          displayValue = _workingLocationController.text;
          actualValue = _workingLocationController.text;
          WidgetsBinding.instance.addPostFrameCallback((final _) {
            if (mounted) {
              _workingLocationFocus.requestFocus();
            }
          });
          break;
        case WorkingLocationSelectionType.none:
          break;
      }

      setState(() {
        _selectionType = newSelectionType;
        _selectedWorkingLocationDisplay = displayValue;
        _editedLogHours =
            _editedLogHours.copyWith(workingLocation: actualValue);
        if (newSelectionType == WorkingLocationSelectionType.inputText) {
          _workingLocationController.text = actualValue ?? '';
        } else {
          _workingLocationController.clear();
        }
      });
    }
  }

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: _unfocus,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          toolbarHeight: 44,
          backgroundColor: Colors.white,
          elevation: 0,
          leading: Padding(
            padding: const EdgeInsets.only(left: 12),
            child: IconButton(
              icon: const Icon(
                Icons.chevron_left,
                color: Colors.black54,
                size: 32,
              ),
              onPressed: widget.onBack,
            ),
          ),
          actions: [
            // Add reset button
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: IconButton(
                icon: const Icon(
                  Icons.restore,
                  size: 28,
                  color: Colors.black54,
                ),
                onPressed: _resetToOriginal,
              ),
            ),
          ],
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          child: Container(
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.only(left: 24, right: 24, bottom: 8),
              child: Column(
                children: [
                  const Divider(height: 2),
                  Builder(
                    builder: (final context) {
                      LogHoursProjectLoadItems? projectToSelect;
                      if (_editedLogHours.projectId != null) {
                        final matchingProjects = widget.projectList
                            .where(
                              (final project) =>
                                  project?.id == _editedLogHours.projectId,
                            )
                            .toList();
                        if (matchingProjects.isNotEmpty) {
                          projectToSelect = matchingProjects.first;
                        }
                      }

                      return _buildSelectableField<LogHoursProjectLoadItems>(
                        icon: Icons.work_outline,
                        label: '${context.l10n.project} *',
                        value:
                            _editedLogHours.projectName ?? context.l10n.noData,
                        isEditable: true,
                        items: widget.projectList,
                        selectedItem:
                            projectToSelect, // Use the safely found item
                        getDisplayText: (final item) =>
                            // ignore: lines_longer_than_80_chars
                            '${item?.code ?? ''} ${item?.name ?? context.l10n.noData}',
                        onChanged: (final item) {
                          setState(() {
                            _editedLogHours = _editedLogHours.copyWith(
                              projectId: item?.id,
                              projectName: item?.name,
                            );
                          });
                        },
                      );
                    },
                  ),
                  const Divider(height: 2),
                  _buildWorkingLocationField(),
                  const Divider(height: 2),
                  _buildTimeField(
                    icon: Icons.access_time_outlined,
                    workingDate: _editedLogHours.workingDate ?? '',
                    checkInTime: _editedLogHours.checkInTime ?? '',
                    checkOutTime: _editedLogHours.checkOutTime ?? '',
                  ),
                  const Divider(height: 2),
                  _buildWorkingHoursComparison(),
                  const Divider(height: 2),
                  _buildLocationField(),
                  const Divider(height: 2),
                  _buildBreakTimeSection(),
                  const Divider(height: 2),
                  _buildDescriptionField(),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildBottomBar(),
      ),
    );
  }

  Widget _buildTimeField({
    required final IconData icon,
    required final String workingDate,
    required final String? checkInTime,
    required final String? checkOutTime,
  }) {
    // Convert beyond-midnight time to normal datetime
    (String checkIn, String checkOut) convertBeyondMidnightTime() {
      final DateTime workingDateTime = DateTime.parse(workingDate);

      if (checkInTime?.isEmpty ?? true) {
        return (
          workingDateTime.formatByDateFormat(DateFormat('yyyy-MM-dd HH:mm:ss')),
          workingDateTime.formatByDateFormat(DateFormat('yyyy-MM-dd HH:mm:ss'))
        );
      }

      // Parse both times
      final DateTime checkInDateTime =
          _parseTimeString(checkInTime!, workingDateTime);

      DateTime? checkOutDateTime;
      try {
        // Safely handle potentially null or empty checkOutTime
        if (checkOutTime != null && checkOutTime.isNotEmpty) {
          checkOutDateTime = _parseTimeString(checkOutTime, workingDateTime);
        }
      } catch (_) {
        // Handle parsing error if necessary, or leave checkOutDateTime as null
      }

      return (
        checkInDateTime.formatByDateFormat(DateFormat('yyyy-MM-dd HH:mm:ss')),
        checkOutDateTime
                ?.formatByDateFormat(DateFormat('yyyy-MM-dd HH:mm:ss')) ??
            '',
      );
    }

    // Format date for display
    String formatDateDisplay(final String dateTime) {
      if (dateTime.isEmpty) {
        return '--/--/----';
      }
      final DateTime? parsed = DateTime.tryParse(dateTime);
      if (parsed == null) {
        return '--/--/----';
      }

      return DateFormat(
        widget.locale == const Locale('ja')
            ? '(E)yyyy年MM月dd日'
            : '(E)yyyy/MM/dd',
        widget.locale.toString(),
      ).format(parsed);
    }

    // Format time for display
    String formatTimeDisplay(final String dateTime) {
      if (dateTime.isEmpty) {
        return '--:--';
      }
      final DateTime? parsed = DateTime.tryParse(dateTime);
      if (parsed == null) {
        return '--:--';
      }

      return DateFormat('HH:mm').format(parsed);
    }

    // Convert times before displaying
    final (String normalizedCheckIn, String normalizedCheckOut) =
        convertBeyondMidnightTime();

    // Select both date and time
    Future<void> selectDateTime(final bool isCheckIn) async {
      final String timeToParse =
          isCheckIn ? normalizedCheckIn : normalizedCheckOut;
      DateTime initialDate;

      try {
        if (timeToParse.isNotEmpty &&
            timeToParse !=
                DateTime.parse(workingDate)
                    .formatByDateFormat(DateFormat('yyyy-MM-dd HH:mm:ss'))) {
          // If timeToParse is not empty and not the default start-of-working-day (which it would be if original was null/empty)
          initialDate = DateTime.parse(timeToParse);
        } else {
          final DateTime currentWorkingDate = DateTime.parse(workingDate);
          final DateTime now = DateTime.now();
          initialDate = DateTime(
            currentWorkingDate.year,
            currentWorkingDate.month,
            currentWorkingDate.day,
            now.hour,
            now.minute,
            now.second,
          );
        }
      } catch (_) {
        final DateTime currentWorkingDate = DateTime.parse(workingDate);
        final DateTime now = DateTime.now();
        initialDate = DateTime(
          currentWorkingDate.year,
          currentWorkingDate.month,
          currentWorkingDate.day,
          now.hour,
          now.minute,
          now.second,
        );
      }

      final DateTime? pickedDate = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (!mounted) {
        return;
      }

      if (pickedDate != null) {
        final TimeOfDay? pickedTime = await showCustomTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(initialDate),
          is24HourFormat: false,
          minuteInterval: 5,
        );

        if (pickedTime != null) {
          final DateTime combinedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );

          // Get the original workingDate for reference
          final DateTime originalWorkingDate = DateTime.parse(
            _editedLogHours.workingDate ??
                DateTime.now().toIso8601String().substring(
                      0,
                      10,
                    ), // Fallback if workingDate is somehow null
          );

          String formatDateTimeToLogHoursTime(
            final DateTime dt,
            final bool isCheckoutField,
          ) {
            final int daysDifference =
                dt.difference(originalWorkingDate).inDays;
            int hour = dt.hour;

            if (isCheckoutField && daysDifference > 0) {
              hour += 24 * daysDifference;
            } else if (isCheckoutField &&
                daysDifference < 0 &&
                dt.isBefore(originalWorkingDate)) {
            } else if (!isCheckoutField && daysDifference != 0) {
              hour = dt.hour;
            }

            final String minuteStr = dt.minute.toString().padLeft(2, '0');
            final String secondStr = dt.second.toString().padLeft(2, '0');
            return "${hour.toString().padLeft(2, '0')}:$minuteStr:$secondStr";
          }

          if (isCheckIn) {
            final String newCheckInTimeStr =
                formatDateTimeToLogHoursTime(combinedDateTime, false);
            String newCheckOutTimeStr = _editedLogHours.checkOutTime ?? '';

            if (_editedLogHours.checkOutTime != null &&
                _editedLogHours.checkOutTime!.isNotEmpty) {
              final DateTime currentCheckOutDateTime = _parseTimeString(
                _editedLogHours.checkOutTime!,
                originalWorkingDate,
              );
              if (combinedDateTime.isAfter(currentCheckOutDateTime)) {
                newCheckOutTimeStr = formatDateTimeToLogHoursTime(
                  combinedDateTime.add(const Duration(minutes: 1)),
                  true,
                );
              }
              final Duration newDifference =
                  currentCheckOutDateTime.isAfter(combinedDateTime)
                      ? currentCheckOutDateTime.difference(combinedDateTime)
                      : combinedDateTime.difference(currentCheckOutDateTime);
              if (newDifference.inHours > 24) {
                newCheckOutTimeStr = formatDateTimeToLogHoursTime(
                  combinedDateTime.add(const Duration(hours: 24)),
                  true,
                );
              }
            }

            setState(() {
              _editedLogHours = _editedLogHours.copyWith(
                checkInTime: newCheckInTimeStr,
                checkOutTime: newCheckOutTimeStr.isEmpty
                    ? null
                    : newCheckOutTimeStr, // Store null if empty
              );
            });
          } else {
            // is CheckOut
            final String newCheckOutTimeStr =
                formatDateTimeToLogHoursTime(combinedDateTime, true);
            String newCheckInTimeStr = _editedLogHours.checkInTime ?? '';

            if (_editedLogHours.checkInTime != null &&
                _editedLogHours.checkInTime!.isNotEmpty) {
              final DateTime currentCheckInDateTime = _parseTimeString(
                _editedLogHours.checkInTime!,
                originalWorkingDate,
              );
              if (combinedDateTime.isBefore(currentCheckInDateTime)) {
                newCheckInTimeStr = formatDateTimeToLogHoursTime(
                  combinedDateTime.subtract(const Duration(minutes: 1)),
                  false,
                );
              }
              final Duration newDifference =
                  currentCheckInDateTime.isAfter(combinedDateTime)
                      ? currentCheckInDateTime.difference(combinedDateTime)
                      : combinedDateTime.difference(currentCheckInDateTime);

              if (newDifference.inHours > 24) {
                newCheckInTimeStr = formatDateTimeToLogHoursTime(
                  combinedDateTime.subtract(const Duration(hours: 24)),
                  false,
                );
              }
            }

            setState(() {
              _editedLogHours = _editedLogHours.copyWith(
                checkInTime: newCheckInTimeStr.isEmpty
                    ? null
                    : newCheckInTimeStr, // Store null if empty
                checkOutTime: newCheckOutTimeStr,
              );
            });
          }
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Icon(icon, size: 24, color: Colors.black54),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                // Check-in date and time
                Expanded(
                  child: Center(
                    child: InkWell(
                      onTap: () async => selectDateTime(true),
                      child: Column(
                        children: [
                          Text(
                            formatDateDisplay(normalizedCheckIn),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black54,
                              fontFamily: 'Noto Sans JP',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            formatTimeDisplay(normalizedCheckIn),
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Noto Sans JP',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Icon(
                    Icons.arrow_forward,
                    size: 20,
                    color: Colors.black54,
                  ),
                ),
                // Check-out date and time
                Expanded(
                  child: Center(
                    child: InkWell(
                      onTap: () async => selectDateTime(false),
                      child: Column(
                        children: [
                          Text(
                            formatDateDisplay(normalizedCheckOut),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black54,
                              fontFamily: 'Noto Sans JP',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            formatTimeDisplay(normalizedCheckOut),
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Noto Sans JP',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingHoursComparison() {
    String calculateTotalHours(final LogHoursDocs logHours) {
      if (logHours.checkInTime == null || logHours.checkOutTime == null) {
        return '0:00';
      }

      final checkIn = DateTime.parse(
        logHours.checkInTime!.contains('-')
            ? logHours.checkInTime!
            : '${logHours.workingDate} ${logHours.checkInTime!}',
      );
      final checkOut = DateTime.parse(
        logHours.checkOutTime!.contains('-')
            ? logHours.checkOutTime!
            : '${logHours.workingDate} ${logHours.checkOutTime!}',
      );

      // Calculate total break duration
      int totalBreakMinutes = 0;
      for (final break_ in logHours.breakList ?? <Break>[]) {
        if (break_.breakIn != null && break_.breakOut != null) {
          final breakInParts = break_.breakIn!.split(':');
          final breakOutParts = break_.breakOut!.split(':');

          final breakInMinutes =
              int.parse(breakInParts[0]) * 60 + int.parse(breakInParts[1]);
          final breakOutMinutes =
              int.parse(breakOutParts[0]) * 60 + int.parse(breakOutParts[1]);

          totalBreakMinutes += breakOutMinutes - breakInMinutes;
        }
      }

      // Calculate total working duration
      final totalMinutes =
          checkOut.difference(checkIn).inMinutes - totalBreakMinutes;
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;

      return '$hours:${minutes.toString().padLeft(2, '0')}';
    }

    final originalHours = calculateTotalHours(_originalLogHours);
    final editedHours = calculateTotalHours(_editedLogHours);
    final bool hoursChanged = originalHours != editedHours;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          const Icon(Icons.timer, size: 24, color: Colors.black54),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Row(
                    children: [
                      Text(
                        // ignore: lines_longer_than_80_chars
                        '${context.l10n.totalWorkingHours}(${context.l10n.original}):',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                          fontFamily: 'Noto Sans JP',
                        ),
                      ),
                      Text(
                        originalHours,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontFamily: 'Noto Sans JP',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        // ignore: lines_longer_than_80_chars
                        '${context.l10n.totalWorkingHours}(${context.l10n.edit}):',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                          fontFamily: 'Noto Sans JP',
                        ),
                      ),
                      Text(
                        editedHours,
                        style: TextStyle(
                          fontSize: 14,
                          color: hoursChanged ? Colors.blue : Colors.black,
                          fontWeight: hoursChanged
                              ? FontWeight.bold
                              : FontWeight.normal,
                          fontFamily: 'Noto Sans JP',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBreakTimeSection() {
    final breakList = _editedLogHours.breakList ?? [];
    final canAddBreak = breakList.length < 3;

    return Column(
      children: [
        Padding(
          padding: canAddBreak
              ? const EdgeInsets.symmetric(vertical: 6)
              : const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              const Icon(
                Icons.timer_outlined,
                size: 24,
                color: Colors.black54,
              ),
              const SizedBox(width: 16),
              Text(
                context.l10n.breakTime,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              if (canAddBreak)
                IconButton(
                  icon: const Icon(Icons.add_circle_outline),
                  onPressed: _addBreakTime,
                  color: Colors.black,
                ),
            ],
          ),
        ),
        if (breakList.isNotEmpty)
          ...breakList.map(
            (final breakItem) => _buildBreakTimeRow(
              breakItem.breakIn,
              breakItem.breakOut,
              breakList.indexOf(breakItem),
            ),
          ),
      ],
    );
  }

  Widget _buildBreakTimeRow(
    final String? start, // Changed to String?
    final String? end, // Changed to String?
    final int index,
  ) {
    final displayStart = (start == null || start.isEmpty) ? '--:--' : start;
    final displayEnd = (end == null || end.isEmpty) ? '--:--' : end;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 24),
            child: InkWell(
              onTap: () async => _selectBreakTime(index, true),
              child: Text(
                displayStart,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontFamily: 'Noto Sans JP',
                ),
              ),
            ),
          ),
          const Icon(
            Icons.east,
            size: 24,
            color: Colors.black54,
          ),
          InkWell(
            onTap: () async => _selectBreakTime(index, false),
            child: Text(
              displayEnd,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontFamily: 'Noto Sans JP',
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _removeBreakTime(index),
            color: Colors.black54,
          ),
        ],
      ),
    );
  }

  void _addBreakTime() {
    setState(() {
      final currentBreaks = List<Break>.from(_editedLogHours.breakList ?? []);
      if (currentBreaks.length < 3) {
        currentBreaks.add(
          Break(),
        );
        _editedLogHours = _editedLogHours.copyWith(breakList: currentBreaks);
      }
    });
  }

  void _removeBreakTime(final int index) {
    setState(() {
      final newBreakList = List<Break>.from(_editedLogHours.breakList ?? []);
      newBreakList.removeAt(index);
      _editedLogHours = _editedLogHours.copyWith(breakList: newBreakList);
    });
  }

  Future<void> _selectBreakTime(final int index, final bool isBreakIn) async {
    final currentBreak = _editedLogHours.breakList![index];
    TimeOfDay initialTime;

    if (isBreakIn) {
      final breakInTime = currentBreak.breakIn;
      if (breakInTime != null && breakInTime.isNotEmpty) {
        final parts = breakInTime.split(':');
        initialTime = TimeOfDay(
          hour: int.parse(parts[0]),
          minute: int.parse(parts[1]),
        );
      } else {
        initialTime = TimeOfDay.now();
      }
    } else {
      final breakOutTime = currentBreak.breakOut;
      if (breakOutTime != null && breakOutTime.isNotEmpty) {
        final parts = breakOutTime.split(':');
        initialTime = TimeOfDay(
          hour: int.parse(parts[0]),
          minute: int.parse(parts[1]),
        );
      } else {
        initialTime = TimeOfDay.now();
      }
    }

    final TimeOfDay? picked = await showCustomTimePicker(
      context: context,
      initialTime: initialTime,
      is24HourFormat: false,
      minuteInterval: 5,
    );

    if (picked != null) {
      final pickedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked.hour,
        picked.minute,
      );

      if (isBreakIn) {
        final breakOutDateTime = pickedDateTime.add(const Duration(seconds: 1));

        final formattedBreakIn = '${picked.hour.toString().padLeft(2, '0')}:'
            '${picked.minute.toString().padLeft(2, '0')}';
        final formattedBreakOut =
            '${breakOutDateTime.hour.toString().padLeft(2, '0')}:'
            '${breakOutDateTime.minute.toString().padLeft(2, '0')}';

        setState(() {
          final newBreakList =
              List<Break>.from(_editedLogHours.breakList ?? []);
          newBreakList[index] = Break(
            breakIn: formattedBreakIn,
            breakOut: formattedBreakOut,
          );
          _editedLogHours = _editedLogHours.copyWith(breakList: newBreakList);
        });
      } else {
        final breakInTime = currentBreak.breakIn!;
        final parts = breakInTime.split(':');
        final breakInDateTime = DateTime(
          DateTime.now().year,
          DateTime.now().month,
          DateTime.now().day,
          int.parse(parts[0]),
          int.parse(parts[1]),
        );

        if (pickedDateTime.isBefore(breakInDateTime) ||
            pickedDateTime == breakInDateTime) {
          if (!mounted) {
            return;
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.l10n.breakInMustBeBeforeBreakOut),
              duration: const Duration(seconds: 2),
            ),
          );
          return;
        }

        final formattedTime = '${picked.hour.toString().padLeft(2, '0')}:'
            '${picked.minute.toString().padLeft(2, '0')}';

        setState(() {
          final newBreakList =
              List<Break>.from(_editedLogHours.breakList ?? []);
          newBreakList[index] = Break(
            breakIn: currentBreak.breakIn,
            breakOut: formattedTime,
          );
          _editedLogHours = _editedLogHours.copyWith(breakList: newBreakList);
        });
      }
    }
  }

  Widget _buildSelectableField<T>({
    required final IconData icon,
    required final String label,
    required final String value,
    required final bool isEditable,
    required final List<T?> items,
    required final T? selectedItem,
    required final String Function(T?) getDisplayText,
    required final Function(T?) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: const Color(0xFF6B7280)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 12,
                    fontFamily: 'Noto Sans JP',
                  ),
                ),
                const SizedBox(height: 4),
                if (isEditable)
                  DropdownButtonFormField<T>(
                    value: selectedItem,
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                    ),
                    isExpanded: true,
                    menuMaxHeight: 300,
                    items: items
                        .where((final item) => item != null)
                        .map(
                          (final item) => DropdownMenuItem<T>(
                            value: item,
                            child: Text(
                              getDisplayText(item),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Noto Sans JP',
                                color: Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        )
                        .toList(),
                    onChanged: onChanged,
                  )
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      value,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Noto Sans JP',
                        color: Colors.black87,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationField() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Check-in location row
          Row(
            children: [
              const Icon(
                Icons.location_on_outlined,
                size: 24,
                color: Colors.black54,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  _editedLogHours.checkInLocation ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontFamily: 'Noto Sans JP',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.location_on_sharp,
                size: 24,
                color: Colors.black54,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  _editedLogHours.checkOutLocation ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontFamily: 'Noto Sans JP',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingLocationField() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          const Icon(Icons.factory_outlined, size: 24, color: Colors.black54),
          const SizedBox(width: 16),
          Expanded(
            child: InkWell(
              onTap: _selectionType == WorkingLocationSelectionType.inputText
                  ? null
                  : _showWorkingLocationSelectionDialog,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: _selectionType == WorkingLocationSelectionType.inputText
                    ? TextFormField(
                        controller: _workingLocationController,
                        focusNode: _workingLocationFocus,
                        decoration: InputDecoration(
                          hintText: context.l10n.enterWorkplace,
                          isDense: true,
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                        ),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          fontFamily: 'Noto Sans JP',
                        ),
                        onChanged: (final value) {
                          setState(() {
                            _selectedWorkingLocationDisplay = value;
                            _editedLogHours = _editedLogHours.copyWith(
                              workingLocation: value,
                            );
                          });
                        },
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${context.l10n.workingLocation} *',
                                  style: const TextStyle(
                                    color: Color(0xFF6B7280),
                                    fontSize: 12,
                                    fontFamily: 'Noto Sans JP',
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _selectedWorkingLocationDisplay ??
                                      context.l10n.selectWorksite,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color:
                                        _selectedWorkingLocationDisplay != null
                                            ? Colors.black87
                                            : Colors.black38,
                                    fontFamily: 'Noto Sans JP',
                                  ),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.black54,
                          ),
                        ],
                      ),
              ),
            ),
          ),
          if (_selectionType != WorkingLocationSelectionType.none)
            IconButton(
              icon: const Icon(Icons.clear, color: Colors.black54),
              onPressed: () {
                setState(() {
                  _selectionType = WorkingLocationSelectionType.none;
                  _selectedWorkingLocationDisplay = null;
                  _workingLocationController.clear();
                  _editedLogHours = _editedLogHours.copyWith();
                  _workingLocationFocus.unfocus();
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildDescriptionField() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.comment_outlined, size: 24, color: Colors.black54),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              focusNode: _descriptionFocus,
              initialValue: _editedLogHours.description,
              textAlign: TextAlign.left,
              decoration: InputDecoration(
                hintText: context.l10n.note,
                hintStyle: const TextStyle(
                  color: Colors.black38,
                  fontSize: 14,
                  fontFamily: 'Noto Sans JP',
                ),
                border: InputBorder.none,
              ),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontFamily: 'Noto Sans JP',
              ),
              maxLines: 3,
              onChanged: (final value) {
                setState(() {
                  _editedLogHours = _editedLogHours.copyWith(
                    description: value,
                  );
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.black12),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.save_outlined),
                    onPressed: () {
                      _unfocus();

                      // Merge times with working date before saving
                      var updatedLogHours = _editedLogHours;

                      // Helper function to merge time with working date
                      String mergeWithWorkingDate(final String? timeStr) {
                        if (timeStr == null || timeStr.isEmpty) {
                          return '';
                        }
                        if (timeStr.contains('-')) {
                          return timeStr; // Already full datetime
                        }

                        final workingDate =
                            DateTime.parse(_editedLogHours.workingDate ?? '');
                        final timeParts = timeStr.split(':');
                        final hours = int.parse(timeParts[0]);
                        final minutes = int.parse(timeParts[1]);
                        final seconds =
                            timeParts.length > 2 ? int.parse(timeParts[2]) : 0;

                        // Handle beyond midnight times (24:00 and beyond)
                        final isNextDay = hours >= 24;
                        final normalizedHours = isNextDay ? hours - 24 : hours;

                        final dateTime = workingDate.add(
                          Duration(
                            days: isNextDay ? 1 : 0,
                            hours: normalizedHours,
                            minutes: minutes,
                            seconds: seconds,
                          ),
                        );

                        return dateTime.formatByDateFormat(
                          DateFormat('yyyy-MM-dd HH:mm:ss'),
                        );
                      }

                      // Update check-in/out times
                      updatedLogHours = updatedLogHours.copyWith(
                        checkInTime:
                            mergeWithWorkingDate(_editedLogHours.checkInTime),
                        checkOutTime:
                            mergeWithWorkingDate(_editedLogHours.checkOutTime),
                      );

                      // Update break times
                      if (_editedLogHours.breakList != null) {
                        final updatedBreaks =
                            _editedLogHours.breakList!.map((final break_) {
                          return Break(
                            breakIn: mergeWithWorkingDate(break_.breakIn),
                            breakOut: mergeWithWorkingDate(break_.breakOut),
                          );
                        }).toList();

                        updatedLogHours = updatedLogHours.copyWith(
                          breakList: updatedBreaks,
                        );
                      }

                      widget.onSave(_editedLogHours.copyWith());
                      widget.onBack();
                    },
                    color: Colors.black54,
                  ),
                  Text(
                    context.l10n.save,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.delete_outline),
                    onPressed: () {
                      widget.onDelete(_editedLogHours);
                      widget.onBack();
                    },
                    color: Colors.black54,
                  ),
                  Text(
                    context.l10n.delete,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  DateTime _parseTimeString(
    final String timeString,
    final DateTime baseDate,
  ) {
    if (timeString.contains('-') && timeString.contains(':')) {
      try {
        return DateTime.parse(timeString);
      } catch (e) {
        return baseDate;
      }
    }

    final List<String> timeParts = timeString.split(':');
    if (timeParts.length < 2) {
      return baseDate;
    }
    int hours = int.parse(timeParts[0]);

    final bool isNextDay = hours >= 24;
    if (isNextDay) {
      hours -= 24;
    }

    return baseDate.add(Duration(days: isNextDay ? 1 : 0)).add(
          Duration(
            hours: hours,
            minutes: int.parse(timeParts[1]),
            seconds: timeParts.length > 2 ? int.parse(timeParts[2]) : 0,
          ),
        );
  }
}
