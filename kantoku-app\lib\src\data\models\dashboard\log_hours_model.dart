// Package imports:
import 'package:json_annotation/json_annotation.dart';

part 'log_hours_model.g.dart';

@JsonSerializable()
class LogHoursModel {
  LogHoursModel({
    required this.docs,
    this.totalWorksDay,
    this.totalAbsentDays,
    this.totalWorkTime,
    this.totalOverTime,
    this.totalPaidLeaveUsed,
    this.totalUnpaidLeaveUsed,
  });

  factory LogHoursModel.fromJson(final Map<String, dynamic> json) =>
      _$LogHoursModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'items')
  final List<LogHoursDocsModel?> docs;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalWorkingDays')
  final int? totalWorksDay;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalAbsentDays')
  final int? totalAbsentDays;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalWorkTime')
  final double? totalWorkTime;

  @Json<PERSON>ey(name: 'totalOverTime')
  final double? totalOverTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalPaidLeaveUsed')
  final double? totalPaidLeaveUsed;

  @Json<PERSON><PERSON>(name: 'totalUnpaidLeaveUsed')
  final double? totalUnpaidLeaveUsed;

  Map<String, dynamic> toJson() => _$LogHoursModelToJson(this);
}

@JsonSerializable()
class LogHoursDocsModel {
  LogHoursDocsModel({
    this.employeeShiftId,
    this.employeeId,
    this.employeeName,
    this.isScheduled,
    this.projectId,
    this.projectName,
    this.projectCode,
    this.workingLocation,
    this.isApproved,
    this.isRequested,
    this.workingDate,
    this.scheduledStartTime,
    this.checkInTime,
    this.checkInLocation,
    this.scheduledEndTime,
    this.checkOutTime,
    this.checkOutLocation,
    this.breakList,
    this.totalWorkTime,
    this.totalBreakTime,
    this.totalOverTime,
    this.description,
    this.createTime,
    this.updateTime,
    this.approvedBy,
    this.approvedTime,
  });

  factory LogHoursDocsModel.fromJson(final Map<String, dynamic> json) =>
      _$LogHoursDocsModelFromJson(json);

  @JsonKey(name: 'employeeShiftId')
  final String? employeeShiftId;

  @JsonKey(name: 'employeeId')
  final String? employeeId;

  @JsonKey(name: 'employeeName')
  final String? employeeName;

  @JsonKey(name: 'isScheduled')
  final bool? isScheduled;

  @JsonKey(name: 'projectId')
  final String? projectId;

  @JsonKey(name: 'projectName')
  final String? projectName;

  @JsonKey(name: 'projectCode')
  final String? projectCode;

  @JsonKey(name: 'workingLocation')
  final String? workingLocation;

  @JsonKey(name: 'isApproved')
  final bool? isApproved;

  @JsonKey(name: 'isRequested')
  final bool? isRequested;

  @JsonKey(name: 'workingDate')
  final String? workingDate;

  @JsonKey(name: 'scheduledStartTime')
  final String? scheduledStartTime;

  @JsonKey(name: 'checkInTime')
  final String? checkInTime;

  @JsonKey(name: 'checkInLocation')
  final String? checkInLocation;

  @JsonKey(name: 'scheduledEndTime')
  final String? scheduledEndTime;

  @JsonKey(name: 'checkOutTime')
  final String? checkOutTime;

  @JsonKey(name: 'checkOutLocation')
  final String? checkOutLocation;

  @JsonKey(name: 'breakList')
  final List<BreakModel>? breakList;

  @JsonKey(name: 'totalWorkTime')
  final double? totalWorkTime;

  @JsonKey(name: 'totalBreakTime')
  final double? totalBreakTime;

  @JsonKey(name: 'totalOverTime')
  final double? totalOverTime;

  @JsonKey(name: 'description')
  final String? description;

  @JsonKey(name: 'createTime')
  final String? createTime;

  @JsonKey(name: 'updateTime')
  final String? updateTime;

  @JsonKey(name: 'approvedBy')
  final String? approvedBy;

  @JsonKey(name: 'approvedTime')
  final String? approvedTime;

  Map<String, dynamic> toJson() => _$LogHoursDocsModelToJson(this);
}

@JsonSerializable()
class BreakModel {
  BreakModel({
    this.breakIn,
    this.breakOut,
  });

  factory BreakModel.fromJson(final Map<String, dynamic> json) =>
      _$BreakModelFromJson(json);

  @JsonKey(name: 'breakInTime')
  final String? breakIn;

  @JsonKey(name: 'breakOutTime')
  final String? breakOut;

  Map<String, dynamic> toJson() => _$BreakModelToJson(this);
}
